from transformers import AutoTokenizer, AutoModelForCausalLM, pipeline
import torch
import soundfile as sf
from xcodec2.modeling_xcodec2 import XCodec2Model
import torchaudio
import gradio as gr
import tempfile
import os

# Set memory optimization environment variables
os.environ["PYTORCH_CUDA_ALLOC_CONF"] = "expandable_segments:True"

# Clear cache and set memory fraction
torch.cuda.empty_cache()
torch.cuda.set_per_process_memory_fraction(0.9)

llasa_3b = 'srinivasbilla/llasa-3b'

# Global variables for lazy loading
tokenizer = None
model = None
Codec_model = None
whisper_turbo_pipe = None

def load_models():
    global tokenizer, model, Codec_model, whisper_turbo_pipe

    if tokenizer is None:
        print("Loading tokenizer...")
        tokenizer = AutoTokenizer.from_pretrained(llasa_3b)
        print("Tokenizer loaded!")

    if model is None:
        print("Loading main model with optimized settings...")
        model = AutoModelForCausalLM.from_pretrained(
            llasa_3b,
            trust_remote_code=True,
            torch_dtype=torch.float16,
            device_map="auto",
            low_cpu_mem_usage=True,
        )
        print("Main model loaded!")

    if Codec_model is None:
        model_path = "srinivasbilla/xcodec2"
        print("Loading codec model...")
        Codec_model = XCodec2Model.from_pretrained(model_path)
        Codec_model.eval()
        if torch.cuda.is_available():
            Codec_model = Codec_model.half().cuda()
        print("Codec model loaded!")

    if whisper_turbo_pipe is None:
        print("Loading Whisper model...")
        whisper_turbo_pipe = pipeline(
            "automatic-speech-recognition",
            model="openai/whisper-large-v3-turbo",
            torch_dtype=torch.float16,
            device='cuda' if torch.cuda.is_available() else 'cpu',
        )
        print("Whisper model loaded!")

    print("All models loaded successfully!")
    return tokenizer, model, Codec_model, whisper_turbo_pipe

def ids_to_speech_tokens(speech_ids):
    return [f"<|s_{speech_id}|>" for speech_id in speech_ids]

def extract_speech_ids(speech_tokens_str):
    speech_ids = []
    for token_str in speech_tokens_str:
        if token_str.startswith('<|s_') and token_str.endswith('|>'):
            try:
                num = int(token_str[4:-2])
                speech_ids.append(num)
            except ValueError:
                print(f"Invalid speech token: {token_str}")
        else:
            print(f"Unexpected token: {token_str}")
    return speech_ids

def infer(sample_audio_path, target_text, progress=gr.Progress()):
    try:
        # Lazy load models
        load_models()

        torch.cuda.empty_cache()

        with tempfile.NamedTemporaryFile(delete=False, suffix=".wav") as f:
            progress(0, 'Loading and trimming audio...')
            waveform, sample_rate = torchaudio.load(sample_audio_path)

            if len(waveform[0]) / sample_rate > 15:
                gr.Warning("Trimming audio to first 15 seconds.")
                waveform = waveform[:, :sample_rate * 15]

            # Convert to mono
            if waveform.size(0) > 1:
                waveform_mono = torch.mean(waveform, dim=0, keepdim=True)
            else:
                waveform_mono = waveform

            # Resample to 16kHz - ensure float32 for resampling
            resampler = torchaudio.transforms.Resample(orig_freq=sample_rate, new_freq=16000)
            prompt_wav = resampler(waveform_mono.float())

            # Save original (float32 CPU) version for Whisper
            whisper_input = prompt_wav.clone()

            # Pass correct format (float32, CPU, numpy) to Whisper
            prompt_text = whisper_turbo_pipe(whisper_input[0].cpu().float().numpy())['text'].strip()
            progress(0.5, 'Transcribed! Generating speech...')

            if len(target_text) == 0:
                return None
            elif len(target_text) > 300:
                gr.Warning("Text is too long. Please keep it under 300 characters.")
                target_text = target_text[:300]

            input_text = prompt_text + ' ' + target_text

            # Prepare for speech synthesis
            with torch.no_grad():
                # Ensure consistent dtype for codec model
                if torch.cuda.is_available():
                    # Convert to half precision only when moving to GPU
                    prompt_wav_codec = prompt_wav.half().cuda()
                else:
                    prompt_wav_codec = prompt_wav.float()

                # Encode audio prompt
                vq_code_prompt = Codec_model.encode_code(input_waveform=prompt_wav_codec)[0, 0, :]
                speech_ids_prefix = ids_to_speech_tokens(vq_code_prompt)

                formatted_text = f"<|TEXT_UNDERSTANDING_START|>{input_text}<|TEXT_UNDERSTANDING_END|>"

                chat = [
                    {"role": "user", "content": "Convert the text to speech:" + formatted_text},
                    {"role": "assistant", "content": "<|SPEECH_GENERATION_START|>" + ''.join(speech_ids_prefix)}
                ]

                input_ids = tokenizer.apply_chat_template(
                    chat,
                    tokenize=True,
                    return_tensors='pt',
                    continue_final_message=True
                )

                device = next(model.parameters()).device
                input_ids = input_ids.to(device)
                speech_end_id = tokenizer.convert_tokens_to_ids('<|SPEECH_GENERATION_END|>')

                estimated_speech_tokens = len(target_text) * 5
                max_length = min(input_ids.shape[1] + estimated_speech_tokens + 100, 2048)

                outputs = model.generate(
                    input_ids,
                    max_length=max_length,
                    eos_token_id=speech_end_id,
                    do_sample=True,
                    top_p=1,
                    temperature=0.8,
                    pad_token_id=tokenizer.eos_token_id
                )

                generated_ids = outputs[0][input_ids.shape[1] - len(speech_ids_prefix):-1]
                speech_tokens = tokenizer.batch_decode(generated_ids, skip_special_tokens=True)
                speech_tokens = extract_speech_ids(speech_tokens)

                if not speech_tokens:
                    gr.Warning("No speech tokens generated. Try different input.")
                    return None

                speech_tokens = torch.tensor(speech_tokens).unsqueeze(0).unsqueeze(0)
                if torch.cuda.is_available():
                    speech_tokens = speech_tokens.cuda()

                gen_wav = Codec_model.decode_code(speech_tokens)
                gen_wav = gen_wav[:, :, prompt_wav_codec.shape[1]:]  # Trim prompt

                progress(1, 'Synthesized!')
                return (16000, gen_wav[0, 0, :].cpu().float().numpy())

    except torch.cuda.OutOfMemoryError:
        torch.cuda.empty_cache()
        gr.Error("GPU out of memory. Try with shorter text or restart the interface.")
        return None
    except Exception as e:
        print(f"Error during inference: {e}")
        gr.Error(f"Error during inference: {str(e)}")
        return None

# Gradio Interface
with gr.Blocks() as app_tts:
    gr.Markdown("# Zero Shot Voice Clone TTS")
    ref_audio_input = gr.Audio(label="Reference Audio", type="filepath")
    gen_text_input = gr.Textbox(label="Text to Generate", lines=10)
    generate_btn = gr.Button("Synthesize", variant="primary")
    audio_output = gr.Audio(label="Synthesized Audio")

    generate_btn.click(
        infer,
        inputs=[ref_audio_input, gen_text_input],
        outputs=[audio_output],
    )

with gr.Blocks() as app_credits:
    gr.Markdown("""
# Credits
* [zhenye234](https://github.com/zhenye234) for the original [repo](https://github.com/zhenye234/LLaSA_training)
* [mrfakename](https://huggingface.co/mrfakename) for the [gradio demo code](https://huggingface.co/spaces/mrfakename/E2-F5-TTS)        
""")

with gr.Blocks() as app:
    gr.Markdown("""
# LLASA 3B TTS
This is a local web UI for LLASA 3B SOTA (imo) Zero Shot Voice Cloning and TTS model.
The checkpoints support English and Chinese.
If you're having issues, try converting your reference audio to WAV or MP3, clipping it to 15s, and shortening your prompt.
""")
    gr.TabbedInterface([app_tts], ["TTS"])

app.launch(server_name="0.0.0.0", server_port=7860, share=False)