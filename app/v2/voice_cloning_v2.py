from fastapi import <PERSON><PERSON><PERSON>, Depends, Query
from fastapi import Form, HTTPException
from fastapi.responses import JSONResponse
from fastapi.openapi.utils import get_openapi
import httpx
import os
from typing import Optional, Dict, Any, Annotated, Literal
import logging
import uuid
from datetime import datetime, timezone, timedelta
from io import BytesIO
from bson import ObjectId
from app.v2.user_management import router as user_management_router
from app.v2.voice_cloning import router as voice_cloning_router
from app.v2.gemini_tts import router as gemini_tts_router
from app.v2.cost_tracking import router as cost_tracking_router
from core.security import get_tenant_info
from core.database import get_async_db_from_tenant_id
from models.user import UserTenantDB

logger = logging.getLogger(__name__)

# Configuration
EXISTING_API_URL_CH = os.getenv("INFERENCE_API_URL", "http://172.16.16.155:8002")
VOICE_CLONING_API_URL = "http://172.16.16.155:8000/api/voice-clone"
TIMBRE_TRANSFER_API_URL = "http://172.16.16.155:8008/timbre-transfer/"
TIMEOUT = 240  # Increased timeout for audio processing

# Define preset voice options (6 predefined audio files)
PresetVoice = Literal[
    "preset_male_1", "preset_male_2", "preset_male_3",
    "preset_female_1", "preset_female_2", "preset_female_3"
]

# Preset voice file mappings in MinIO storage
PRESET_VOICE_PATHS = {
    "preset_male_1": "voice_storage/presets/male_voice_1.wav",
    "preset_male_2": "voice_storage/presets/male_voice_2.wav",
    "preset_male_3": "voice_storage/presets/male_voice_3.wav",
    "preset_female_1": "voice_storage/presets/female_voice_1.wav",
    "preset_female_2": "voice_storage/presets/female_voice_2.wav",
    "preset_female_3": "voice_storage/presets/female_voice_3.wav"
}

# Helper function to fetch style presets from MongoDB
async def get_style_presets(user_tenant_info: UserTenantDB) -> Dict[str, Dict[str, Any]]:
    """
    Fetch style presets from MongoDB config collection.

    Returns:
        Dictionary of style presets with their parameters
    """
    try:
        # Get database connection
        tenant_database = get_async_db_from_tenant_id(user_tenant_info.tenant_id)

        # Fetch style presets from config collection
        config_doc = await tenant_database.config.find_one({"style_presets": {"$exists": True}})

        if not config_doc or "style_presets" not in config_doc:
            logger.warning("Style presets not found in config collection, using default presets")
            # Return default presets as fallback
            return {
                "default": {
                    "exaggeration": 0.5,
                    "cfg_weight": 0.5,
                    "temperature": 0.5,
                    "seed": 0
                }
            }

        style_presets = config_doc["style_presets"]
        logger.info(f"Loaded {len(style_presets)} style presets from database: {list(style_presets.keys())}")
        return style_presets

    except Exception as e:
        logger.error(f"Error fetching style presets from database: {str(e)}")
        # Return default preset as fallback
        return {
            "default": {
                "exaggeration": 0.5,
                "cfg_weight": 0.5,
                "temperature": 0.5,
                "seed": 0
            }
        }


# Helper function to retrieve preset voice file from MinIO
async def get_preset_voice_file(preset_voice: str, user_tenant_info: UserTenantDB) -> bytes:
    """
    Retrieve a preset voice file from MinIO storage.

    Args:
        preset_voice: The preset voice identifier
        user_tenant_info: User tenant information containing MinIO client

    Returns:
        bytes: The audio file content

    Raises:
        HTTPException: If the preset voice file is not found or cannot be retrieved
    """
    try:
        if preset_voice not in PRESET_VOICE_PATHS:
            raise HTTPException(status_code=400, detail=f"Invalid preset voice: {preset_voice}")

        minio_path = PRESET_VOICE_PATHS[preset_voice]
        minio_client = user_tenant_info.minio_client
        bucket_name = user_tenant_info.minio_bucket_name

        logger.info(f"Retrieving preset voice file: {minio_path}")

        # Get the file from MinIO
        response = minio_client.get_object(bucket_name, minio_path)
        file_content = response.read()
        response.close()
        response.release_conn()

        if len(file_content) == 0:
            raise HTTPException(status_code=404, detail=f"Preset voice file is empty: {preset_voice}")

        logger.info(f"Successfully retrieved preset voice file: {len(file_content)} bytes")
        return file_content

    except Exception as e:
        if isinstance(e, HTTPException):
            raise
        logger.error(f"Failed to retrieve preset voice file {preset_voice}: {str(e)}")
        raise HTTPException(
            status_code=404,
            detail=f"Preset voice file not found: {preset_voice}. Please ensure preset voice files are uploaded to MinIO."
        )


# Create router
router = FastAPI(title="Voice Cloning_v2")
router.include_router(user_management_router)
router.include_router(voice_cloning_router)
router.include_router(gemini_tts_router)
router.include_router(cost_tracking_router)

# Custom OpenAPI schema modification 

# @router.post("/speak")
# async def inference_endpoint(
#     voice_id: str = Form(...),
#     reference_text: Optional[str] = Form(None),
#     generation_text: str = Form(...),
#     user_tenant_info: UserTenantDB = Depends(get_tenant_info)
# ):
#     """
#     Endpoint that takes a voice_id and text, retrieves the voice file from database,
#     then calls inference API. Uploads both input and output audio files to MinIO
#     and returns JSON with presigned URLs.
#     Requires authentication via Bearer token.

#     Args:
#         voice_id: Voice clone ID to use for inference
#         reference_text: Optional reference text (can be None/empty, will be passed as-is)
#         generation_text: Required generation text
#         user_tenant_info: Authenticated user information (injected by dependency)

#     Returns:
#         JSON response with input_media and output_media presigned URLs
#     """
#     try:
#         # Log authenticated API usage
#         logger.info(f"Inference API called by user: {user_tenant_info.user.username} from tenant: {user_tenant_info.tenant_id}")

#         # Validate voice_id format
#         try:
#             ObjectId(voice_id)
#         except Exception:
#             raise HTTPException(status_code=400, detail="Invalid voice_id format")

#         # Validate generation_text is not empty
#         if not generation_text or len(generation_text.strip()) == 0:
#             raise HTTPException(
#                 status_code=400,
#                 detail="generation_text cannot be empty"
#             )

#         # Get database connection
#         tenant_database = get_async_db_from_tenant_id(user_tenant_info.tenant_id)

#         # Retrieve voice from database
#         logger.info(f"Retrieving voice with voice_id: {voice_id}")
#         voice = await tenant_database.cloned_voices.find_one({"_id": ObjectId(voice_id)})

#         if not voice:
#             raise HTTPException(status_code=404, detail="Voice not found")

#         # Get voice metadata
#         user_id = voice.get("user_id")
#         user_name = voice.get("voice_name")
#         audio_files = voice.get("audio_files", [])

#         if not audio_files:
#             raise HTTPException(status_code=400, detail="No audio files found for this voice")

#         # Use the first audio file (since we only store one file per voice now)
#         audio_file_info = audio_files[0]
#         voice_minio_path = audio_file_info["minio_path"]
#         original_filename = audio_file_info["file_name"]

#         logger.info(f"Using audio file: {voice_minio_path}")

#         # Get MinIO client and bucket info
#         logger.info("Getting MinIO client for file retrieval")
#         minio_client = user_tenant_info.minio_client
#         bucket_name = user_tenant_info.minio_bucket_name

#         # Download the audio file from MinIO
#         try:
#             response = minio_client.get_object(bucket_name, voice_minio_path)
#             file_content = response.read()
#             response.close()
#             logger.info(f"Retrieved audio file size: {len(file_content)} bytes")
#         except Exception as e:
#             logger.error(f"Failed to retrieve audio file from MinIO: {str(e)}")
#             raise HTTPException(status_code=500, detail=f"Failed to retrieve voice audio file: {str(e)}")

#         # Step 1: Upload input file to MinIO for inference tracking
#         logger.info("Uploading input audio file to MinIO")
#         input_file_extension = os.path.splitext(original_filename)[1] if original_filename else '.wav'
#         input_minio_path = f"inference/{user_id}/{user_name}/input_{uuid.uuid4()}{input_file_extension}"

#         try:
#             input_buffer = BytesIO(file_content)
#             minio_client.put_object(
#                 bucket_name=bucket_name,
#                 object_name=input_minio_path,
#                 data=input_buffer,
#                 length=len(file_content),
#                 content_type="audio/wav"  # Default content type
#             )
#             logger.info(f"Input file uploaded to MinIO: {input_minio_path}")
#         except Exception as e:
#             logger.error(f"Failed to upload input file to MinIO: {str(e)}")
#             raise HTTPException(status_code=500, detail=f"Failed to save input file: {str(e)}")

#         # Prepare multipart form data for the existing API
#         files = {
#             "ref_audio": (original_filename, file_content, "audio/wav")
#         }

#         data = {
#             "ref_text": reference_text,  # Always include, even if None/empty
#             "gen_text": generation_text
#         }

#         # Step 2: Call existing inference API with multipart form data
#         logger.info("Calling external inference API")
#         async with httpx.AsyncClient() as client:
#             response = await client.post(
#                 f"{EXISTING_API_URL_F5}/synthesize/",
#                 files=files,
#                 data=data,
#                 timeout=TIMEOUT
#             )
#             response.raise_for_status()

#         # Get the generated audio content
#         output_audio_content = response.content
#         logger.info(f"Generated audio size: {len(output_audio_content)} bytes")

#         # Step 3: Upload output file to MinIO
#         logger.info("Uploading generated audio file to MinIO")
#         output_filename = f"output_{uuid.uuid4()}.wav"
#         output_minio_path = f"inference/{user_id}/{user_name}/{output_filename}"

#         try:
#             output_buffer = BytesIO(output_audio_content)
#             minio_client.put_object(
#                 bucket_name=bucket_name,
#                 object_name=output_minio_path,
#                 data=output_buffer,
#                 length=len(output_audio_content),
#                 content_type=response.headers.get('content-type', 'audio/wav')
#             )
#             logger.info(f"Output file uploaded to MinIO: {output_minio_path}")
#         except Exception as e:
#             logger.error(f"Failed to upload output file to MinIO: {str(e)}")
#             raise HTTPException(status_code=500, detail=f"Failed to save output file: {str(e)}")

#         # Step 4: Generate presigned URLs for both files
#         logger.info("Generating presigned URLs")
#         try:
#             input_presigned_url = minio_client.presigned_get_object(
#                 bucket_name=bucket_name,
#                 object_name=input_minio_path,
#                 expires=timedelta(hours=24)
#             )

#             output_presigned_url = minio_client.presigned_get_object(
#                 bucket_name=bucket_name,
#                 object_name=output_minio_path,
#                 expires=timedelta(hours=24)
#             )
#             logger.info("Presigned URLs generated successfully")
#         except Exception as e:
#             logger.error(f"Failed to generate presigned URLs: {str(e)}")
#             input_presigned_url = f"Error generating URL: {str(e)}"
#             output_presigned_url = f"Error generating URL: {str(e)}"

#         # Step 5: Save metadata to database
#         logger.info("Saving inference metadata to inference_results collection")
#         try:
#             tenant_database = get_async_db_from_tenant_id(user_tenant_info.tenant_id)

#             inference_doc = {
#                 "user_id": user_id,
#                 "user_name": user_name,
#                 "username": user_tenant_info.user.username,
#                 "input_minio_path": input_minio_path,
#                 "output_minio_path": output_minio_path,
#                 "input_presigned_url": input_presigned_url,
#                 "output_presigned_url": output_presigned_url,
#                 "reference_text": reference_text,
#                 "generation_text": generation_text,
#                 "input_file_size": len(file_content),
#                 "output_file_size": len(output_audio_content),
#                 "created_at": datetime.now(timezone.utc),
#                 "file_type": "inference_audio",
#                 "model_type": "f5_tts"  # Identify which model was used
#             }

#             result = await tenant_database.inference_results.insert_one(inference_doc)
#             logger.info(f"Metadata saved to inference_results collection with _id: {result.inserted_id}")
#         except Exception as e:
#             logger.error(f"Failed to save metadata to inference_results collection: {str(e)}")
#             # Don't fail the request if metadata save fails

#         # Step 6: Return JSON response with presigned URLs
#         logger.info("Inference completed successfully")
#         return JSONResponse(
#             content={
#                 "message": "Voice inference completed successfully",
#                 "input_media": input_presigned_url,
#                 "output_media": output_presigned_url,
#                 "generation_text": generation_text,
#                 "reference_text": reference_text,
#                 "user_id": user_id,
#                 "user_name": user_name,
#                 "input_file_size": len(file_content),
#                 "output_file_size": len(output_audio_content),
#                 "created_at": datetime.now().isoformat(),
#                 "minio_paths": {
#                     "input": input_minio_path,
#                     "output": output_minio_path
#                 }
#             },
#             status_code=200
#         )
        
#     except httpx.HTTPStatusError as e:
#         raise HTTPException(
#             status_code=e.response.status_code,
#             detail=f"Inference API error: {e.response.text}"
#         )
#     except httpx.RequestError as e:
#         raise HTTPException(
#             status_code=503,
#             detail=f"Service unavailable: Unable to connect to inference API - {str(e)}"
#         )
#     except Exception as e:
#         raise HTTPException(
#             status_code=500,
#             detail=f"Internal server error: {str(e)}"
#         )
    

@router.post("/speak")
async def inference(
    voice_id: str = Form(...),
    generation_text: str = Form(...),
    user_tenant_info: UserTenantDB = Depends(get_tenant_info),
    style_preset: str = Form(default="default")
):
    """
    Endpoint that takes a voice_id and text, retrieves the voice file from database,
    then calls  inference API. Uploads both input and output audio files to MinIO
    then calls  inference API. Uploads both input and output audio files to MinIO
    and returns JSON with presigned URLs.
    Requires authentication via Bearer token.

    Args:
        voice_id: Voice clone ID to use for inference
        generation_text: Required generation text
        style_preset: Style preset (Literal: 'default', 'natural', 'dramatic', 'presentation')
        user_tenant_info: Authenticated user information (injected by dependency)
        style_preset: Style preset name (dynamically loaded from MongoDB config collection)

    Style Presets:
        Style presets are dynamically loaded from the MongoDB config collection.
        Available presets and their parameters depend on the configuration stored in the database.
        Use the /style-presets endpoint to see all available options.
        If no presets are found, a default preset will be used.

    Returns:
        JSON response with input_media and output_media presigned URLs
    """
    try:
        # Log authenticated API usage
        logger.info(f" Inference API called by user: {user_tenant_info.user.username} from tenant: {user_tenant_info.tenant_id}")

        # Validate voice_id format
        try:
            ObjectId(voice_id)
        except Exception:
            raise HTTPException(status_code=400, detail="Invalid voice_id format")

        # Validate generation_text is not empty
        if not generation_text or len(generation_text.strip()) == 0:
            raise HTTPException(
                status_code=400,
                detail="generation_text cannot be empty"
            )

        # Fetch style presets from database
        style_presets = await get_style_presets(user_tenant_info)

        # Validate style_preset exists
        if style_preset.lower() not in style_presets:
            available_presets = list(style_presets.keys())
            raise HTTPException(
                status_code=400,
                detail=f"Invalid style_preset '{style_preset}'. Available presets: {available_presets}"
            )

        # Apply style preset
        preset = style_presets[style_preset.lower()]
        final_exaggeration = preset["exaggeration"]
        final_cfg_weight = preset["cfg_weight"]
        final_temperature = preset["temperature"]
        final_seed = preset["seed"]

        logger.info(f"Using style preset '{style_preset}': exaggeration={final_exaggeration}, cfg_weight={final_cfg_weight}, temperature={final_temperature}, seed={final_seed}")

        # Get database connection
        tenant_database = get_async_db_from_tenant_id(user_tenant_info.tenant_id)

        # Retrieve voice from database
        logger.info(f"Retrieving voice with voice_id: {voice_id}") 
        logger.info(f"Retrieving voice with voice_id: {voice_id}") 
        voice = await tenant_database.cloned_voices.find_one({"_id": ObjectId(voice_id)})

        if not voice:
            raise HTTPException(status_code=404, detail="Voice not found")

        # Get voice metadata
        user_id = voice.get("user_id")
        user_name = voice.get("voice_name")
        audio_files = voice.get("audio_files", [])

        if not audio_files:
            raise HTTPException(status_code=400, detail="No audio files found for this voice")

        # Use the first audio file (since we only store one file per voice now)
        audio_file_info = audio_files[0]
        voice_minio_path = audio_file_info["minio_path"]
        original_filename = audio_file_info["file_name"]

        logger.info(f"Using audio file: {voice_minio_path}")

        # Get MinIO client and bucket info
        logger.info("Getting MinIO client for file retrieval")
        minio_client = user_tenant_info.minio_client
        bucket_name = user_tenant_info.minio_bucket_name

        # Download the audio file from MinIO
        try:
            response = minio_client.get_object(bucket_name, voice_minio_path)
            file_content = response.read()
            response.close()
            logger.info(f"Retrieved audio file size: {len(file_content)} bytes")
        except Exception as e:
            logger.error(f"Failed to retrieve audio file from MinIO: {str(e)}")
            raise HTTPException(status_code=500, detail=f"Failed to retrieve voice audio file: {str(e)}")

        # Step 1: Upload input file to MinIO for inference tracking
        logger.info("Uploading input audio file to MinIO")
        input_file_extension = os.path.splitext(original_filename)[1] if original_filename else '.wav'
        input_minio_path = f"inference_chatterbox/{user_id}/{user_name}/input_{uuid.uuid4()}{input_file_extension}"
        input_minio_path = f"inference_chatterbox/{user_id}/{user_name}/input_{uuid.uuid4()}{input_file_extension}"

        try:
            input_buffer = BytesIO(file_content)
            minio_client.put_object(
                bucket_name=bucket_name,
                object_name=input_minio_path,
                data=input_buffer,
                length=len(file_content),
                content_type="audio/wav"  # Default content type
            )
            logger.info(f"Input file uploaded to MinIO: {input_minio_path}")
        except Exception as e:
            logger.error(f"Failed to upload input file to MinIO: {str(e)}")
            raise HTTPException(status_code=500, detail=f"Failed to save input file: {str(e)}")

        # Prepare multipart form data for the Chatterbox API
        # Prepare multipart form data for the Chatterbox API
        files = {
            "ref_audio": (original_filename, file_content, "audio/wav")
        }

        data = {
            "text": generation_text,  # Chatterbox uses 'text' instead of separate ref_text/gen_text
            "exaggeration": final_exaggeration,
            "temperature": final_temperature,
            "seed": final_seed,
            "cfg_weight": final_cfg_weight,
            "text": generation_text,  # Chatterbox uses 'text' instead of separate ref_text/gen_text
            "exaggeration": final_exaggeration,
            "temperature": final_temperature,
            "seed": final_seed,
            "cfg_weight": final_cfg_weight
        }

        # Step 2: Call Chatterbox inference API with multipart form data
        logger.info("Calling  inference API")
        async with httpx.AsyncClient() as client:
            response = await client.post(
                f"{EXISTING_API_URL_CH}/synthesize/",
                files=files,
                data=data,
                timeout=TIMEOUT
            )
            response.raise_for_status()

        # Get the generated audio content
        output_audio_content = response.content
        logger.info(f"Generated audio size: {len(output_audio_content)} bytes")

        # Step 3: Upload output file to MinIO
        logger.info("Uploading generated audio file to MinIO")
        output_filename = f"output_{uuid.uuid4()}.wav"
        output_minio_path = f"inference_chatterbox/{user_id}/{user_name}/{output_filename}"
        output_minio_path = f"inference_chatterbox/{user_id}/{user_name}/{output_filename}"

        try:
            output_buffer = BytesIO(output_audio_content)
            minio_client.put_object(
                bucket_name=bucket_name,
                object_name=output_minio_path,
                data=output_buffer,
                length=len(output_audio_content),
                content_type=response.headers.get('content-type', 'audio/wav')
            )
            logger.info(f"Output file uploaded to MinIO: {output_minio_path}")
        except Exception as e:
            logger.error(f"Failed to upload output file to MinIO: {str(e)}")
            raise HTTPException(status_code=500, detail=f"Failed to save output file: {str(e)}")

        # Step 4: Generate presigned URLs for both files
        logger.info("Generating presigned URLs")
        try:
            input_presigned_url = minio_client.presigned_get_object(
                bucket_name=bucket_name,
                object_name=input_minio_path,
                expires=timedelta(hours=24)
            )

            output_presigned_url = minio_client.presigned_get_object(
                bucket_name=bucket_name,
                object_name=output_minio_path,
                expires=timedelta(hours=24)
            )
            logger.info("Presigned URLs generated successfully")
        except Exception as e:
            logger.error(f"Failed to generate presigned URLs: {str(e)}")
            input_presigned_url = f"Error generating URL: {str(e)}"
            output_presigned_url = f"Error generating URL: {str(e)}"

        # Step 5: Save metadata to database
        logger.info("Saving Chatterbox inference metadata to inference_results collection")
        logger.info("Saving Chatterbox inference metadata to inference_results collection")
        try:
            inference_doc = {
                "user_id": user_id,
                "user_name": user_name,
                "input_minio_path": input_minio_path,
                "output_minio_path": output_minio_path,
                "input_presigned_url": input_presigned_url,
                "output_presigned_url": output_presigned_url,
                "generation_text": generation_text,
                "style_preset": style_preset,
                "exaggeration": final_exaggeration,
                "temperature": final_temperature,
                "seed": final_seed,
                "cfg_weight": final_cfg_weight,
                "style_preset": style_preset,
                "exaggeration": final_exaggeration,
                "temperature": final_temperature,
                "seed": final_seed,
                "cfg_weight": final_cfg_weight,
                "input_file_size": len(file_content),
                "output_file_size": len(output_audio_content),
                "created_at": datetime.now(timezone.utc),
                "file_type": "inference_audio",
            }

            result = await tenant_database.inference_results.insert_one(inference_doc)
            logger.info(f"Metadata saved to inference_results collection with _id: {result.inserted_id}")
            result = await tenant_database.inference_results.insert_one(inference_doc)
            logger.info(f"Metadata saved to inference_results collection with _id: {result.inserted_id}")
        except Exception as e:
            logger.error(f"Failed to save metadata to inference_results collection: {str(e)}")
            logger.error(f"Failed to save metadata to inference_results collection: {str(e)}")
            # Don't fail the request if metadata save fails

        # Step 6: Return JSON response with presigned URLs
        logger.info(" inference completed successfully")
        return JSONResponse(
            content={
                "message": " voice inference completed successfully",
                "input_media": input_presigned_url,
                "output_media": output_presigned_url,
                "generation_text": generation_text,
                "style_preset": style_preset,
                "parameters": {
                    "exaggeration": final_exaggeration,
                    "temperature": final_temperature,
                    "seed": final_seed,
                    "cfg_weight": final_cfg_weight
                },
                "user_id": user_id,
                "user_name": user_name,
                "input_file_size": len(file_content),
                "output_file_size": len(output_audio_content),
                "created_at": datetime.now().isoformat(),
                "minio_paths": {
                    "input": input_minio_path,
                    "output": output_minio_path
                }
            },
            status_code=200
        )


    except HTTPException:
        # Re-raise HTTPExceptions (like 400 validation errors) without modification
        raise
    except httpx.HTTPStatusError as e:
        raise HTTPException(
            status_code=e.response.status_code,
            detail=f" Inferencing API error: {e.response.text}"
        )
    except httpx.RequestError as e:
        raise HTTPException(
            status_code=503,
            detail=f"Service unavailable: Unable to connect to Inferencing API - {str(e)}"
        )
    except Exception as e:
        raise HTTPException(
            status_code=500,
            detail=f"Internal server error: {str(e)}"
        )


@router.post("/speak_v2")
async def speak_v2(
    text: str = Form(...),
    preset_voice: PresetVoice = Form(...),
    user_voice_id: str = Form(...),
    user_tenant_info: UserTenantDB = Depends(get_tenant_info)
):
    """
    Combined TTS generation with voice cloning and timbre transfer workflow.

    This endpoint performs a two-step process:
    1. Generate speech from text using a selected preset voice via voice cloning API
    2. Apply timbre transfer using the user's cloned voice as reference

    Args:
        text: The text to be converted to speech
        preset_voice: Selection from 6 predefined audio files (preset_male_1, preset_male_2, preset_male_3, preset_female_1, preset_female_2, preset_female_3)
        user_voice_id: Reference to user's cloned voice from cloned_voices collection
        user_tenant_info: Authenticated user information (injected by dependency)

    Returns:
        JSON response with presigned URLs to the final timbre-transferred audio file

    Requires authentication via Bearer token.
    """
    logger.info(f"Starting speak_v2 workflow for text: '{text[:50]}...', preset_voice: {preset_voice}, user_voice_id: {user_voice_id}")

    try:
        # Get database connection
        tenant_database = get_async_db_from_tenant_id(user_tenant_info.tenant_id)

        # Get MinIO client and bucket info
        minio_client = user_tenant_info.minio_client
        bucket_name = user_tenant_info.minio_bucket_name

        # Step 1: Retrieve user's cloned voice from database
        logger.info(f"Retrieving user cloned voice with ID: {user_voice_id}")
        user_voice = await tenant_database.cloned_voices.find_one({"_id": ObjectId(user_voice_id)})

        if not user_voice:
            raise HTTPException(status_code=404, detail="User voice not found")

        user_id = user_voice.get("user_id")
        user_name = user_voice.get("voice_name")
        user_audio_files = user_voice.get("audio_files", [])

        if not user_audio_files:
            raise HTTPException(status_code=400, detail="No audio files found for user voice")

        # Get user's cloned voice file from MinIO
        user_audio_file_info = user_audio_files[0]  # Use first audio file
        user_voice_minio_path = user_audio_file_info["minio_path"]

        logger.info(f"Retrieving user voice file: {user_voice_minio_path}")
        user_voice_response = minio_client.get_object(bucket_name, user_voice_minio_path)
        user_voice_content = user_voice_response.read()
        user_voice_response.close()
        user_voice_response.release_conn()

        if len(user_voice_content) == 0:
            raise HTTPException(status_code=400, detail="User voice file is empty")

        logger.info(f"Retrieved user voice file: {len(user_voice_content)} bytes")

        # Step 2: Retrieve preset voice file from MinIO
        logger.info(f"Retrieving preset voice: {preset_voice}")
        preset_voice_content = await get_preset_voice_file(preset_voice, user_tenant_info)

        # Step 3: Generate TTS using preset voice (First API call - Voice Cloning API)
        logger.info("Step 1: Generating TTS using preset voice via voice cloning API")

        # Prepare files for Voice Cloning API
        files = {
            "reference_audio": (f"{preset_voice}.wav", preset_voice_content, "audio/wav")
        }

        data = {
            "text": text
        }

        # Call Voice Cloning API
        async with httpx.AsyncClient() as client:
            tts_response = await client.post(
                VOICE_CLONING_API_URL,
                files=files,
                data=data,
                timeout=TIMEOUT
            )
            tts_response.raise_for_status()

        tts_audio_content = tts_response.content
        logger.info(f"TTS generation completed: {len(tts_audio_content)} bytes")

        # Step 4: Apply timbre transfer (Second API call)
        logger.info("Step 2: Applying timbre transfer")

        # Prepare files for timbre transfer API
        timbre_files = {
            "source_audio": ("tts_output.wav", tts_audio_content, "audio/wav"),
            "reference_audio": (f"user_voice_{user_voice_id}.wav", user_voice_content, "audio/wav")
        }

        timbre_data = {
            "noise_cancellation": True,
            "normalize_audio_flag": True
        }

        # Call timbre transfer API
        async with httpx.AsyncClient() as client:
            timbre_response = await client.post(
                TIMBRE_TRANSFER_API_URL,
                files=timbre_files,
                data=timbre_data,
                timeout=TIMEOUT
            )
            timbre_response.raise_for_status()

        final_audio_content = timbre_response.content
        logger.info(f"Timbre transfer completed: {len(final_audio_content)} bytes")

        # Step 5: Upload final audio to MinIO
        logger.info("Uploading final processed audio to MinIO")
        final_filename = f"speak_v2_output_{uuid.uuid4()}.wav"
        final_minio_path = f"speak_v2/{user_id}/{user_name}/{final_filename}"

        final_audio_buffer = BytesIO(final_audio_content)
        minio_client.put_object(
            bucket_name=bucket_name,
            object_name=final_minio_path,
            data=final_audio_buffer,
            length=len(final_audio_content),
            content_type="audio/wav"
        )

        # Step 6: Generate presigned URL
        logger.info("Generating presigned URL for final audio")
        try:
            final_presigned_url = minio_client.presigned_get_object(
                bucket_name=bucket_name,
                object_name=final_minio_path,
                expires=timedelta(hours=24)
            )
        except Exception as e:
            logger.error(f"Failed to generate presigned URL: {str(e)}")
            final_presigned_url = f"Error generating URL: {str(e)}"

        # Step 7: Save metadata to database
        logger.info("Saving speak_v2 workflow metadata to database")
        workflow_doc = {
            "user_id": user_id,
            "user_name": user_name,
            "user_voice_id": user_voice_id,
            "preset_voice": preset_voice,
            "input_text": text,
            "final_minio_path": final_minio_path,
            "final_presigned_url": final_presigned_url,
            "workflow_type": "speak_v2_tts_timbre_transfer",
            "created_at": datetime.now(timezone.utc),
            "file_size": len(final_audio_content),
            "processing_steps": {
                "voice_cloning_tts": {
                    "preset_voice_used": preset_voice,
                    "api_used": "voice_cloning_api",
                    "tts_output_size": len(tts_audio_content)
                },
                "timbre_transfer": {
                    "source_audio_size": len(tts_audio_content),
                    "reference_audio_size": len(user_voice_content),
                    "final_output_size": len(final_audio_content)
                }
            }
        }

        await tenant_database.speak_v2_results.insert_one(workflow_doc)
        logger.info("Workflow metadata saved to database")

        # Step 8: Return response
        logger.info("speak_v2 workflow completed successfully")
        return JSONResponse(
            content={
                "message": "speak_v2 workflow completed successfully",
                "final_audio_url": final_presigned_url,
                "text": text,
                "preset_voice": preset_voice,
                "user_voice_id": user_voice_id,
                "user_id": user_id,
                "user_name": user_name,
                "final_file_size": len(final_audio_content),
                "created_at": datetime.now().isoformat(),
                "minio_path": final_minio_path,
                "workflow_steps": {
                    "voice_cloning_tts_size": len(tts_audio_content),
                    "timbre_transfer_size": len(final_audio_content)
                }
            },
            status_code=200
        )

    except HTTPException:
        # Re-raise HTTPExceptions (like 400 validation errors) without modification
        raise
    except httpx.HTTPStatusError as e:
        logger.error(f"External API error: {e.response.status_code} - {e.response.text}")
        raise HTTPException(
            status_code=e.response.status_code,
            detail=f"External API error: {e.response.text}"
        )
    except httpx.RequestError as e:
        logger.error(f"Request error: {str(e)}")
        raise HTTPException(
            status_code=503,
            detail=f"Service unavailable: Unable to connect to external APIs - {str(e)}"
        )
    except Exception as e:
        logger.error(f"Unexpected error in speak_v2 workflow: {str(e)}")
        raise HTTPException(
            status_code=500,
            detail=f"Internal server error: {str(e)}"
        )


@router.get("/get_presets")
async def get_presets(
    user_tenant_info: UserTenantDB = Depends(get_tenant_info)
):
    """
    Get all available style presets from MongoDB config collection.
    Requires authentication via Bearer token.

    Returns:
        JSON response with all available style presets and their parameters
    """
    try:
        # Log authenticated API usage
        logger.info(f"Get presets API called by user: {user_tenant_info.user.username} from tenant: {user_tenant_info.tenant_id}")

        # Fetch style presets from database using existing function
        style_presets = await get_style_presets(user_tenant_info)

        # Count total presets
        total_presets = len(style_presets)

        # Log successful retrieval
        logger.info(f"Successfully retrieved {total_presets} style presets for user: {user_tenant_info.user.username}")

        # Return JSON response with all presets
        return JSONResponse(
            content={
                "message": "Style presets retrieved successfully",
                "total_presets": total_presets,
                "presets": style_presets,
                "available_preset_names": list(style_presets.keys()),
                "retrieved_at": datetime.now().isoformat(),
                "user": user_tenant_info.user.username,
                "tenant_id": user_tenant_info.tenant_id
            },
            status_code=200
        )

    except HTTPException:
        # Re-raise HTTPExceptions without modification
        raise
    except Exception as e:
        logger.error(f"Error retrieving style presets for user {user_tenant_info.user.username}: {str(e)}")
        raise HTTPException(
            status_code=500,
            detail=f"Failed to retrieve style presets: {str(e)}"
        )


@router.get("/preset_voices")
async def get_preset_voices(
    user_tenant_info: UserTenantDB = Depends(get_tenant_info)
):
    """
    Get all available preset voices for the speak_v2 endpoint.
    Requires authentication via Bearer token.

    Returns:
        List of available preset voice options with descriptions
    """
    logger.info("Fetching available preset voices")

    try:
        preset_voices = [
            {
                "value": "preset_male_1",
                "label": "Male Voice 1",
                "description": "Professional male voice with clear pronunciation",
                "gender": "male"
            },
            {
                "value": "preset_male_2",
                "label": "Male Voice 2",
                "description": "Warm, friendly male voice with natural flow",
                "gender": "male"
            },
            {
                "value": "preset_male_3",
                "label": "Male Voice 3",
                "description": "Deep, authoritative male voice with rich timbre",
                "gender": "male"
            },
            {
                "value": "preset_female_1",
                "label": "Female Voice 1",
                "description": "Elegant, sophisticated female voice with smooth delivery",
                "gender": "female"
            },
            {
                "value": "preset_female_2",
                "label": "Female Voice 2",
                "description": "Clear, melodious female voice with expressive tone",
                "gender": "female"
            },
            {
                "value": "preset_female_3",
                "label": "Female Voice 3",
                "description": "Soft, gentle female voice with calming presence",
                "gender": "female"
            }
        ]

        logger.info(f"Returning {len(preset_voices)} preset voices")
        return JSONResponse(
            content={
                "message": "Preset voices retrieved successfully",
                "total_voices": len(preset_voices),
                "voices": preset_voices
            },
            status_code=200
        )

    except Exception as e:
        logger.error(f"Error fetching preset voices: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Failed to fetch preset voices: {str(e)}")

